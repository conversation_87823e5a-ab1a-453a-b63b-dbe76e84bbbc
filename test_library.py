#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبارات نظام إدارة المكتبة
Library Management System Tests
"""

import unittest
import datetime
from library_simulation import Book, Member, LibrarySystem

class TestBook(unittest.TestCase):
    """اختبارات فئة الكتاب"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.book = Book("B001", "Test Book", "Test Author", "978-1234567890")
    
    def test_book_creation(self):
        """اختبار إنشاء كتاب"""
        self.assertEqual(self.book.book_id, "B001")
        self.assertEqual(self.book.title, "Test Book")
        self.assertEqual(self.book.author, "Test Author")
        self.assertEqual(self.book.isbn, "978-1234567890")
        self.assertFalse(self.book.is_borrowed)
        self.assertIsNone(self.book.borrowed_by)
    
    def test_book_string_representation(self):
        """اختبار تمثيل الكتاب كنص"""
        expected = "الكتاب: Test Book | المؤلف: Test Author | الحالة: متاح"
        self.assertEqual(str(self.book), expected)

class TestMember(unittest.TestCase):
    """اختبارات فئة العضو"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.member = Member("M001", "Test Member", "<EMAIL>")
    
    def test_member_creation(self):
        """اختبار إنشاء عضو"""
        self.assertEqual(self.member.member_id, "M001")
        self.assertEqual(self.member.name, "Test Member")
        self.assertEqual(self.member.email, "<EMAIL>")
        self.assertEqual(len(self.member.borrowed_books), 0)
        self.assertEqual(self.member.join_date, datetime.date.today())
    
    def test_member_string_representation(self):
        """اختبار تمثيل العضو كنص"""
        expected = "العضو: Test Member | الرقم: M001 | الكتب المستعارة: 0"
        self.assertEqual(str(self.member), expected)

class TestLibrarySystem(unittest.TestCase):
    """اختبارات نظام المكتبة"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.library = LibrarySystem()
    
    def test_add_book_success(self):
        """اختبار إضافة كتاب بنجاح"""
        result = self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        self.assertTrue(result)
        self.assertIn("B001", self.library.books)
        self.assertEqual(self.library.books["B001"].title, "Test Book")
    
    def test_add_duplicate_book(self):
        """اختبار إضافة كتاب مكرر"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        result = self.library.add_book("B001", "Another Book", "Another Author", "978-0987654321")
        self.assertFalse(result)
        self.assertEqual(len(self.library.books), 1)
    
    def test_add_member_success(self):
        """اختبار إضافة عضو بنجاح"""
        result = self.library.add_member("M001", "Test Member", "<EMAIL>")
        self.assertTrue(result)
        self.assertIn("M001", self.library.members)
        self.assertEqual(self.library.members["M001"].name, "Test Member")
    
    def test_add_duplicate_member(self):
        """اختبار إضافة عضو مكرر"""
        self.library.add_member("M001", "Test Member", "<EMAIL>")
        result = self.library.add_member("M001", "Another Member", "<EMAIL>")
        self.assertFalse(result)
        self.assertEqual(len(self.library.members), 1)
    
    def test_borrow_book_success(self):
        """اختبار استعارة كتاب بنجاح"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        self.library.add_member("M001", "Test Member", "<EMAIL>")
        
        result = self.library.borrow_book("M001", "B001")
        self.assertTrue(result)
        
        book = self.library.books["B001"]
        member = self.library.members["M001"]
        
        self.assertTrue(book.is_borrowed)
        self.assertEqual(book.borrowed_by, "Test Member")
        self.assertIn("B001", member.borrowed_books)
        self.assertEqual(len(self.library.transactions), 1)
    
    def test_borrow_nonexistent_book(self):
        """اختبار استعارة كتاب غير موجود"""
        self.library.add_member("M001", "Test Member", "<EMAIL>")
        result = self.library.borrow_book("M001", "B999")
        self.assertFalse(result)
    
    def test_borrow_by_nonexistent_member(self):
        """اختبار استعارة من عضو غير موجود"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        result = self.library.borrow_book("M999", "B001")
        self.assertFalse(result)
    
    def test_borrow_already_borrowed_book(self):
        """اختبار استعارة كتاب مستعار بالفعل"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        self.library.add_member("M001", "Test Member 1", "<EMAIL>")
        self.library.add_member("M002", "Test Member 2", "<EMAIL>")
        
        self.library.borrow_book("M001", "B001")
        result = self.library.borrow_book("M002", "B001")
        self.assertFalse(result)
    
    def test_return_book_success(self):
        """اختبار إرجاع كتاب بنجاح"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        self.library.add_member("M001", "Test Member", "<EMAIL>")
        
        self.library.borrow_book("M001", "B001")
        result = self.library.return_book("M001", "B001")
        self.assertTrue(result)
        
        book = self.library.books["B001"]
        member = self.library.members["M001"]
        
        self.assertFalse(book.is_borrowed)
        self.assertIsNone(book.borrowed_by)
        self.assertNotIn("B001", member.borrowed_books)
        self.assertEqual(len(self.library.transactions), 2)  # borrow + return
    
    def test_return_not_borrowed_book(self):
        """اختبار إرجاع كتاب غير مستعار"""
        self.library.add_book("B001", "Test Book", "Test Author", "978-1234567890")
        self.library.add_member("M001", "Test Member", "<EMAIL>")
        
        result = self.library.return_book("M001", "B001")
        self.assertFalse(result)
    
    def test_search_books(self):
        """اختبار البحث عن الكتب"""
        self.library.add_book("B001", "Python Programming", "John Doe", "978-1234567890")
        self.library.add_book("B002", "Java Basics", "Jane Smith", "978-0987654321")
        self.library.add_book("B003", "Advanced Python", "Bob Johnson", "978-1111222233")
        
        # البحث بالعنوان
        results = self.library.search_books("Python")
        self.assertEqual(len(results), 2)
        
        # البحث بالمؤلف
        results = self.library.search_books("John")
        self.assertEqual(len(results), 2)  # John Doe and Bob Johnson
        
        # البحث بـ ISBN
        results = self.library.search_books("978-1234567890")
        self.assertEqual(len(results), 1)
        
        # البحث بكلمة غير موجودة
        results = self.library.search_books("NonExistent")
        self.assertEqual(len(results), 0)

class TestIntegration(unittest.TestCase):
    """اختبارات التكامل"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        self.library = LibrarySystem()
        
        # إضافة بيانات تجريبية
        self.library.add_book("B001", "Book 1", "Author 1", "978-1111111111")
        self.library.add_book("B002", "Book 2", "Author 2", "978-2222222222")
        self.library.add_member("M001", "Member 1", "<EMAIL>")
        self.library.add_member("M002", "Member 2", "<EMAIL>")
    
    def test_complete_workflow(self):
        """اختبار سير العمل الكامل"""
        # استعارة كتاب
        self.assertTrue(self.library.borrow_book("M001", "B001"))
        
        # التحقق من حالة الكتاب والعضو
        book = self.library.books["B001"]
        member = self.library.members["M001"]
        
        self.assertTrue(book.is_borrowed)
        self.assertEqual(book.borrowed_by, "Member 1")
        self.assertIn("B001", member.borrowed_books)
        
        # إرجاع الكتاب
        self.assertTrue(self.library.return_book("M001", "B001"))
        
        # التحقق من حالة الكتاب والعضو بعد الإرجاع
        self.assertFalse(book.is_borrowed)
        self.assertIsNone(book.borrowed_by)
        self.assertNotIn("B001", member.borrowed_books)
        
        # التحقق من المعاملات
        self.assertEqual(len(self.library.transactions), 2)
        self.assertEqual(self.library.transactions[0]['type'], 'borrow')
        self.assertEqual(self.library.transactions[1]['type'], 'return')

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("=== تشغيل اختبارات نظام إدارة المكتبة ===")
    print("Running Library Management System Tests")
    print("="*50)

    # إنشاء مجموعة الاختبارات
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # إضافة اختبارات الفئات
    test_suite.addTest(loader.loadTestsFromTestCase(TestBook))
    test_suite.addTest(loader.loadTestsFromTestCase(TestMember))
    test_suite.addTest(loader.loadTestsFromTestCase(TestLibrarySystem))
    test_suite.addTest(loader.loadTestsFromTestCase(TestIntegration))

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # عرض النتائج
    print("\n" + "="*50)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        print("✅ All tests passed!")
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"❌ {len(result.failures)} tests failed")
        print(f"❌ حدث {len(result.errors)} خطأ")
        print(f"❌ {len(result.errors)} errors occurred")

    return result.wasSuccessful()

if __name__ == "__main__":
    run_tests()
