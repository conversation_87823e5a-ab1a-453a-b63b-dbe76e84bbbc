#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة الرسومية لنظام إدارة المكتبة
Run GUI Library Management System
"""

import sys
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import tkinter
        import datetime
        return True
    except ImportError as e:
        print(f"خطأ: مكتبة مطلوبة غير موجودة - {e}")
        return False

def main():
    """تشغيل التطبيق"""
    print("=== تشغيل نظام إدارة المكتبة ===")
    print("Starting Library Management System GUI")
    print("="*40)
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("فشل في التحقق من المتطلبات")
        return
    
    try:
        # استيراد الواجهة الرسومية
        from gui_library import LibraryGUI
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        
        # إعداد النافذة
        root.title("نظام إدارة المكتبة - Library Management System")
        root.geometry("1200x800")
        root.minsize(800, 600)
        
        # تعيين أيقونة (اختياري)
        try:
            # يمكن إضافة أيقونة هنا إذا كانت متوفرة
            # root.iconbitmap('library_icon.ico')
            pass
        except:
            pass
        
        # إنشاء التطبيق
        app = LibraryGUI(root)
        
        print("تم تشغيل الواجهة الرسومية بنجاح!")
        print("GUI started successfully!")
        
        # تشغيل حلقة الأحداث
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"خطأ في الاستيراد: {e}"
        print(error_msg)
        
        # إظهار رسالة خطأ رسومية إذا أمكن
        try:
            root = tk.Tk()
            root.withdraw()  # إخفاء النافذة الرئيسية
            messagebox.showerror("خطأ", error_msg)
        except:
            pass
            
    except Exception as e:
        error_msg = f"خطأ غير متوقع: {e}"
        print(error_msg)
        
        try:
            messagebox.showerror("خطأ", error_msg)
        except:
            pass

if __name__ == "__main__":
    main()
