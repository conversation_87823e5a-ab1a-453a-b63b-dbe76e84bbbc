#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الملف الرئيسي لنظام إدارة المكتبة
Main file for Library Management System

يوفر قائمة خيارات لتشغيل أجزاء مختلفة من النظام
Provides menu options to run different parts of the system
"""

import sys
import os

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    نظام إدارة المكتبة                      ║
║              Library Management System                       ║
║                                                              ║
║                    محاكاة البرامج المتقدمة                  ║
║              Advanced Program Simulation                     ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_menu():
    """طباعة القائمة الرئيسية"""
    menu = """
🔹 اختر نوع التشغيل - Choose Running Mode:

1️⃣  المحاكاة الأساسية (Basic Simulation)
    تشغيل المحاكاة التلقائية مع بيانات تجريبية

2️⃣  الواجهة التفاعلية النصية (Interactive CLI)
    واجهة نصية تفاعلية مع قوائم

3️⃣  المميزات المتقدمة (Advanced Features)
    عرض المميزات المتقدمة مع الغرامات والحجوزات

4️⃣  الواجهة الرسومية (GUI Interface)
    واجهة رسومية متكاملة مع تبويبات

5️⃣  تشغيل الاختبارات (Run Tests)
    تشغيل جميع الاختبارات للتأكد من سلامة النظام

6️⃣  عرض معلومات المشروع (Project Info)
    معلومات تفصيلية عن المشروع والملفات

0️⃣  خروج (Exit)

═══════════════════════════════════════════════════════════════
"""
    print(menu)

def run_basic_simulation():
    """تشغيل المحاكاة الأساسية"""
    print("\n🚀 تشغيل المحاكاة الأساسية...")
    print("Starting Basic Simulation...")
    try:
        import library_simulation
        library_simulation.main()
    except Exception as e:
        print(f"❌ خطأ: {e}")

def run_interactive_cli():
    """تشغيل الواجهة التفاعلية"""
    print("\n🚀 تشغيل الواجهة التفاعلية...")
    print("Starting Interactive CLI...")
    try:
        import interactive_library
        interactive_library.main()
    except Exception as e:
        print(f"❌ خطأ: {e}")

def run_advanced_features():
    """تشغيل المميزات المتقدمة"""
    print("\n🚀 تشغيل المميزات المتقدمة...")
    print("Starting Advanced Features...")
    try:
        import advanced_features
        advanced_features.demo_advanced_features()
    except Exception as e:
        print(f"❌ خطأ: {e}")

def run_gui():
    """تشغيل الواجهة الرسومية"""
    print("\n🚀 تشغيل الواجهة الرسومية...")
    print("Starting GUI Interface...")
    try:
        import gui_library
        gui_library.main()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print("تأكد من أن tkinter مثبت ومتاح")
        print("Make sure tkinter is installed and available")

def run_tests():
    """تشغيل الاختبارات"""
    print("\n🚀 تشغيل الاختبارات...")
    print("Running Tests...")
    try:
        import test_library
        success = test_library.run_tests()
        if success:
            print("\n✅ جميع الاختبارات نجحت!")
        else:
            print("\n❌ بعض الاختبارات فشلت!")
    except Exception as e:
        print(f"❌ خطأ: {e}")

def show_project_info():
    """عرض معلومات المشروع"""
    info = """
📋 معلومات المشروع - Project Information:

📁 الملفات الرئيسية - Main Files:
   • library_simulation.py    - المحاكاة الأساسية
   • interactive_library.py   - الواجهة التفاعلية
   • advanced_features.py     - المميزات المتقدمة
   • gui_library.py          - الواجهة الرسومية
   • test_library.py         - الاختبارات
   • main.py                 - هذا الملف

📊 الإحصائيات - Statistics:
   • عدد الملفات: 8+ ملفات
   • عدد الاختبارات: 16 اختبار
   • اللغات المدعومة: العربية والإنجليزية
   • أنواع الواجهات: CLI, GUI, Interactive

🔧 المتطلبات - Requirements:
   • Python 3.6+
   • tkinter (للواجهة الرسومية)
   • لا توجد مكتبات خارجية أخرى

🎯 المميزات - Features:
   ✅ إدارة الكتب والأعضاء
   ✅ نظام الاستعارة والإرجاع
   ✅ الغرامات والحجوزات
   ✅ التصنيفات والتقارير
   ✅ تصدير البيانات (JSON/CSV)
   ✅ واجهة رسومية متكاملة
   ✅ اختبارات شاملة

📞 للدعم - For Support:
   راجع ملف README.md للتفاصيل الكاملة
   Check README.md for complete details
"""
    print(info)

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'library_simulation.py',
        'interactive_library.py', 
        'advanced_features.py',
        'gui_library.py',
        'test_library.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  تحذير: الملفات التالية مفقودة - Missing files:")
        for file in missing_files:
            print(f"   • {file}")
        print()
    
    return len(missing_files) == 0

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من الملفات
    if not check_files():
        print("❌ بعض الملفات مفقودة. تأكد من وجود جميع ملفات المشروع.")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("👆 اختر رقماً (Enter a number): ").strip()
            
            if choice == '1':
                run_basic_simulation()
            elif choice == '2':
                run_interactive_cli()
            elif choice == '3':
                run_advanced_features()
            elif choice == '4':
                run_gui()
            elif choice == '5':
                run_tests()
            elif choice == '6':
                show_project_info()
            elif choice == '0':
                print("\n👋 شكراً لاستخدام نظام إدارة المكتبة!")
                print("Thank you for using Library Management System!")
                break
            else:
                print("❌ خيار غير صحيح. يرجى اختيار رقم من 0-6")
                print("Invalid choice. Please select a number from 0-6")
            
            # انتظار المستخدم قبل العودة للقائمة
            if choice != '0':
                input("\n⏸️  اضغط Enter للعودة للقائمة الرئيسية...")
                print("\n" + "="*60)
        
        except KeyboardInterrupt:
            print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
            print("Program interrupted by user")
            break
        except Exception as e:
            print(f"\n❌ خطأ غير متوقع: {e}")
            print("Unexpected error occurred")

if __name__ == "__main__":
    main()
