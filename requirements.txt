# متطلبات مشروع نظام إدارة المكتبة
# Library Management System Requirements

# المكتبات الأساسية (مدمجة مع Python)
# Core libraries (built-in with Python)
# - tkinter (GUI)
# - datetime (date/time handling)
# - json (data export)
# - csv (report export)
# - unittest (testing)
# - typing (type hints)

# لا توجد مكتبات خارجية مطلوبة للتشغيل الأساسي
# No external libraries required for basic operation

# مكتبات التطوير الاختيارية (للمطورين فقط)
# Optional development libraries (for developers only)

# Testing
pytest>=6.0.0
pytest-cov>=2.10.0

# Code formatting
black>=21.0.0
isort>=5.0.0

# Code linting
flake8>=3.8.0
pylint>=2.6.0

# Documentation
sphinx>=3.0.0
sphinx-rtd-theme>=0.5.0

# Type checking
mypy>=0.800

# Development tools
pre-commit>=2.0.0

# ملاحظات التثبيت:
# Installation notes:
# 
# للتشغيل الأساسي:
# For basic operation:
# لا حاجة لتثبيت أي مكتبات إضافية
# No additional libraries need to be installed
#
# للتطوير:
# For development:
# pip install -r requirements.txt
#
# أو تثبيت المشروع في وضع التطوير:
# Or install project in development mode:
# pip install -e .[dev]
