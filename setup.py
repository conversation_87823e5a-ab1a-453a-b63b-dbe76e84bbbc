#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد مشروع نظام إدارة المكتبة
Library Management System Setup
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "نظام إدارة المكتبة - Library Management System"

# معلومات المشروع
setup(
    name="library-management-system",
    version="2.0.0",
    author="محاكاة البرامج",
    author_email="<EMAIL>",
    description="نظام إدارة المكتبة المتقدم - Advanced Library Management System",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/example/library-management-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Education",
        "Topic :: Software Development :: Libraries :: Application Frameworks",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
        "Operating System :: OS Independent",
        "Natural Language :: Arabic",
        "Natural Language :: English",
    ],
    python_requires=">=3.6",
    install_requires=[
        # لا توجد مكتبات خارجية مطلوبة
        # جميع المكتبات المستخدمة مدمجة مع Python
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
        ],
        "gui": [
            # tkinter مدمج مع Python
        ],
    },
    entry_points={
        "console_scripts": [
            "library-sim=library_simulation:main",
            "library-interactive=interactive_library:main",
            "library-advanced=advanced_features:demo_advanced_features",
            "library-gui=gui_library:main",
            "library-test=test_library:run_tests",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="library management system simulation education arabic",
    project_urls={
        "Bug Reports": "https://github.com/example/library-management-system/issues",
        "Source": "https://github.com/example/library-management-system",
        "Documentation": "https://github.com/example/library-management-system/wiki",
    },
)
