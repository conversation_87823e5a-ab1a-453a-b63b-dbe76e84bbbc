#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة رسومية لنظام إدارة المكتبة
GUI Library Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import datetime
from advanced_features import AdvancedLibrarySystem

class LibraryGUI:
    """واجهة رسومية لنظام إدارة المكتبة"""

    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المكتبة - Library Management System")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')

        # إنشاء النظام
        self.library = AdvancedLibrarySystem()

        # تحميل بيانات تجريبية
        self.load_sample_data()

        # إنشاء الواجهة
        self.create_widgets()

    def load_sample_data(self):
        """تحميل بيانات تجريبية"""
        # إضافة كتب
        self.library.add_book_with_category("B001", "البرمجة بـ Python", "أحمد محمد", "978-111", "تقنية", 3)
        self.library.add_book_with_category("B002", "الذكاء الاصطناعي", "سارة أحمد", "978-222", "تقنية", 2)
        self.library.add_book_with_category("B003", "الأسود يليق بك", "أحلام مستغانمي", "978-333", "أدب", 1)
        self.library.add_book_with_category("B004", "تاريخ الحضارات", "محمد علي", "978-444", "تاريخ", 2)
        self.library.add_book_with_category("B005", "أساسيات الرياضيات", "عمر خالد", "978-555", "علوم", 2)

        # إضافة أعضاء
        self.library.add_member("M001", "أحمد محمد", "<EMAIL>")
        self.library.add_member("M002", "فاطمة علي", "<EMAIL>")
        self.library.add_member("M003", "محمد حسن", "<EMAIL>")
        self.library.add_member("M004", "سارة أحمد", "<EMAIL>")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="نظام إدارة المكتبة",
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)

        subtitle_label = tk.Label(title_frame, text="Library Management System",
                                 font=('Arial', 12), fg='#ecf0f1', bg='#2c3e50')
        subtitle_label.pack()

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # تبويب الكتب
        self.books_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.books_frame, text="الكتب - Books")
        self.create_books_tab()

        # تبويب الأعضاء
        self.members_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.members_frame, text="الأعضاء - Members")
        self.create_members_tab()

        # تبويب الاستعارة
        self.borrow_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.borrow_frame, text="الاستعارة - Borrowing")
        self.create_borrow_tab()

        # تبويب التقارير
        self.reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.reports_frame, text="التقارير - Reports")
        self.create_reports_tab()

    def create_books_tab(self):
        """إنشاء تبويب الكتب"""
        # إطار إضافة كتاب
        add_frame = ttk.LabelFrame(self.books_frame, text="إضافة كتاب جديد - Add New Book")
        add_frame.pack(fill='x', padx=10, pady=5)

        # حقول الإدخال
        ttk.Label(add_frame, text="رقم الكتاب:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.book_id_entry = ttk.Entry(add_frame, width=15)
        self.book_id_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(add_frame, text="العنوان:").grid(row=0, column=2, sticky='w', padx=5, pady=2)
        self.book_title_entry = ttk.Entry(add_frame, width=25)
        self.book_title_entry.grid(row=0, column=3, padx=5, pady=2)

        ttk.Label(add_frame, text="المؤلف:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
        self.book_author_entry = ttk.Entry(add_frame, width=20)
        self.book_author_entry.grid(row=1, column=1, padx=5, pady=2)

        ttk.Label(add_frame, text="ISBN:").grid(row=1, column=2, sticky='w', padx=5, pady=2)
        self.book_isbn_entry = ttk.Entry(add_frame, width=15)
        self.book_isbn_entry.grid(row=1, column=3, padx=5, pady=2)

        ttk.Label(add_frame, text="التصنيف:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
        self.book_category_combo = ttk.Combobox(add_frame, values=["تقنية", "أدب", "تاريخ", "علوم", "فلسفة"], width=12)
        self.book_category_combo.grid(row=2, column=1, padx=5, pady=2)

        ttk.Label(add_frame, text="عدد النسخ:").grid(row=2, column=2, sticky='w', padx=5, pady=2)
        self.book_copies_entry = ttk.Entry(add_frame, width=10)
        self.book_copies_entry.insert(0, "1")
        self.book_copies_entry.grid(row=2, column=3, padx=5, pady=2)

        ttk.Button(add_frame, text="إضافة كتاب", command=self.add_book).grid(row=3, column=1, pady=10)

        # قائمة الكتب
        list_frame = ttk.LabelFrame(self.books_frame, text="قائمة الكتب - Books List")
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # جدول الكتب
        columns = ('ID', 'Title', 'Author', 'Category', 'Status', 'Copies')
        self.books_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # تعريف الأعمدة
        self.books_tree.heading('ID', text='الرقم')
        self.books_tree.heading('Title', text='العنوان')
        self.books_tree.heading('Author', text='المؤلف')
        self.books_tree.heading('Category', text='التصنيف')
        self.books_tree.heading('Status', text='الحالة')
        self.books_tree.heading('Copies', text='النسخ')

        # تحديد عرض الأعمدة
        self.books_tree.column('ID', width=80)
        self.books_tree.column('Title', width=200)
        self.books_tree.column('Author', width=150)
        self.books_tree.column('Category', width=100)
        self.books_tree.column('Status', width=150)
        self.books_tree.column('Copies', width=80)

        # شريط التمرير
        books_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.books_tree.yview)
        self.books_tree.configure(yscrollcommand=books_scrollbar.set)

        self.books_tree.pack(side='left', fill='both', expand=True)
        books_scrollbar.pack(side='right', fill='y')

        # تحديث قائمة الكتب
        self.refresh_books_list()

    def create_members_tab(self):
        """إنشاء تبويب الأعضاء"""
        # إطار إضافة عضو
        add_frame = ttk.LabelFrame(self.members_frame, text="إضافة عضو جديد - Add New Member")
        add_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(add_frame, text="رقم العضو:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.member_id_entry = ttk.Entry(add_frame, width=15)
        self.member_id_entry.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(add_frame, text="الاسم:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.member_name_entry = ttk.Entry(add_frame, width=25)
        self.member_name_entry.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(add_frame, text="البريد الإلكتروني:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.member_email_entry = ttk.Entry(add_frame, width=30)
        self.member_email_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5)

        ttk.Button(add_frame, text="إضافة عضو", command=self.add_member).grid(row=1, column=3, padx=5, pady=5)

        # قائمة الأعضاء
        list_frame = ttk.LabelFrame(self.members_frame, text="قائمة الأعضاء - Members List")
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('ID', 'Name', 'Email', 'Borrowed', 'Join Date')
        self.members_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        self.members_tree.heading('ID', text='الرقم')
        self.members_tree.heading('Name', text='الاسم')
        self.members_tree.heading('Email', text='البريد الإلكتروني')
        self.members_tree.heading('Borrowed', text='الكتب المستعارة')
        self.members_tree.heading('Join Date', text='تاريخ الانضمام')

        self.members_tree.column('ID', width=100)
        self.members_tree.column('Name', width=200)
        self.members_tree.column('Email', width=250)
        self.members_tree.column('Borrowed', width=120)
        self.members_tree.column('Join Date', width=120)

        members_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.members_tree.yview)
        self.members_tree.configure(yscrollcommand=members_scrollbar.set)

        self.members_tree.pack(side='left', fill='both', expand=True)
        members_scrollbar.pack(side='right', fill='y')

        self.refresh_members_list()

    def create_borrow_tab(self):
        """إنشاء تبويب الاستعارة"""
        # إطار الاستعارة
        borrow_frame = ttk.LabelFrame(self.borrow_frame, text="استعارة كتاب - Borrow Book")
        borrow_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(borrow_frame, text="رقم العضو:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.borrow_member_combo = ttk.Combobox(borrow_frame, width=15)
        self.borrow_member_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(borrow_frame, text="رقم الكتاب:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.borrow_book_combo = ttk.Combobox(borrow_frame, width=15)
        self.borrow_book_combo.grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(borrow_frame, text="مدة الاستعارة (أيام):").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.borrow_days_entry = ttk.Entry(borrow_frame, width=10)
        self.borrow_days_entry.insert(0, "14")
        self.borrow_days_entry.grid(row=1, column=1, padx=5, pady=5)

        ttk.Button(borrow_frame, text="استعارة", command=self.borrow_book).grid(row=1, column=2, padx=5, pady=5)
        ttk.Button(borrow_frame, text="تحديث القوائم", command=self.refresh_combos).grid(row=1, column=3, padx=5, pady=5)

        # إطار الإرجاع
        return_frame = ttk.LabelFrame(self.borrow_frame, text="إرجاع كتاب - Return Book")
        return_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(return_frame, text="رقم العضو:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.return_member_combo = ttk.Combobox(return_frame, width=15)
        self.return_member_combo.grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(return_frame, text="رقم الكتاب:").grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.return_book_combo = ttk.Combobox(return_frame, width=15)
        self.return_book_combo.grid(row=0, column=3, padx=5, pady=5)

        ttk.Button(return_frame, text="إرجاع", command=self.return_book).grid(row=1, column=1, padx=5, pady=5)

        # قائمة المعاملات الحالية
        current_frame = ttk.LabelFrame(self.borrow_frame, text="الكتب المستعارة حالياً - Currently Borrowed")
        current_frame.pack(fill='both', expand=True, padx=10, pady=5)

        columns = ('Member', 'Book', 'Borrow Date', 'Due Date', 'Days Left')
        self.borrowed_tree = ttk.Treeview(current_frame, columns=columns, show='headings', height=10)

        self.borrowed_tree.heading('Member', text='العضو')
        self.borrowed_tree.heading('Book', text='الكتاب')
        self.borrowed_tree.heading('Borrow Date', text='تاريخ الاستعارة')
        self.borrowed_tree.heading('Due Date', text='تاريخ الإرجاع')
        self.borrowed_tree.heading('Days Left', text='الأيام المتبقية')

        borrowed_scrollbar = ttk.Scrollbar(current_frame, orient='vertical', command=self.borrowed_tree.yview)
        self.borrowed_tree.configure(yscrollcommand=borrowed_scrollbar.set)

        self.borrowed_tree.pack(side='left', fill='both', expand=True)
        borrowed_scrollbar.pack(side='right', fill='y')

        self.refresh_borrowed_list()
        self.refresh_combos()

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(self.reports_frame, text="الإحصائيات - Statistics")
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=8, width=80)
        self.stats_text.pack(padx=10, pady=10)

        # أزرار التقارير
        buttons_frame = ttk.Frame(self.reports_frame)
        buttons_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(buttons_frame, text="تحديث الإحصائيات", command=self.update_statistics).pack(side='left', padx=5)
        ttk.Button(buttons_frame, text="تصدير JSON", command=self.export_json).pack(side='left', padx=5)
        ttk.Button(buttons_frame, text="تصدير CSV", command=self.export_csv).pack(side='left', padx=5)

        # تحديث الإحصائيات
        self.update_statistics()

    def add_book(self):
        """إضافة كتاب جديد"""
        book_id = self.book_id_entry.get().strip()
        title = self.book_title_entry.get().strip()
        author = self.book_author_entry.get().strip()
        isbn = self.book_isbn_entry.get().strip()
        category = self.book_category_combo.get().strip()

        try:
            copies = int(self.book_copies_entry.get().strip())
        except ValueError:
            messagebox.showerror("خطأ", "عدد النسخ يجب أن يكون رقماً")
            return

        if not all([book_id, title, author, isbn, category]):
            messagebox.showerror("خطأ", "جميع الحقول مطلوبة")
            return

        if self.library.add_book_with_category(book_id, title, author, isbn, category, copies):
            messagebox.showinfo("نجح", f"تم إضافة الكتاب: {title}")
            # مسح الحقول
            self.book_id_entry.delete(0, tk.END)
            self.book_title_entry.delete(0, tk.END)
            self.book_author_entry.delete(0, tk.END)
            self.book_isbn_entry.delete(0, tk.END)
            self.book_category_combo.set("")
            self.book_copies_entry.delete(0, tk.END)
            self.book_copies_entry.insert(0, "1")

            self.refresh_books_list()
            self.refresh_combos()
        else:
            messagebox.showerror("خطأ", "فشل في إضافة الكتاب")

    def add_member(self):
        """إضافة عضو جديد"""
        member_id = self.member_id_entry.get().strip()
        name = self.member_name_entry.get().strip()
        email = self.member_email_entry.get().strip()

        if not all([member_id, name, email]):
            messagebox.showerror("خطأ", "جميع الحقول مطلوبة")
            return

        if self.library.add_member(member_id, name, email):
            messagebox.showinfo("نجح", f"تم إضافة العضو: {name}")
            # مسح الحقول
            self.member_id_entry.delete(0, tk.END)
            self.member_name_entry.delete(0, tk.END)
            self.member_email_entry.delete(0, tk.END)

            self.refresh_members_list()
            self.refresh_combos()
        else:
            messagebox.showerror("خطأ", "فشل في إضافة العضو")

    def borrow_book(self):
        """استعارة كتاب"""
        member_id = self.borrow_member_combo.get().strip()
        book_id = self.borrow_book_combo.get().strip()

        try:
            days = int(self.borrow_days_entry.get().strip())
        except ValueError:
            messagebox.showerror("خطأ", "مدة الاستعارة يجب أن تكون رقماً")
            return

        if not all([member_id, book_id]):
            messagebox.showerror("خطأ", "يرجى اختيار العضو والكتاب")
            return

        if self.library.borrow_book_advanced(member_id, book_id, days):
            messagebox.showinfo("نجح", "تم استعارة الكتاب بنجاح")
            self.refresh_books_list()
            self.refresh_borrowed_list()
            self.refresh_combos()
        else:
            messagebox.showerror("خطأ", "فشل في استعارة الكتاب")

    def return_book(self):
        """إرجاع كتاب"""
        member_id = self.return_member_combo.get().strip()
        book_id = self.return_book_combo.get().strip()

        if not all([member_id, book_id]):
            messagebox.showerror("خطأ", "يرجى اختيار العضو والكتاب")
            return

        result = self.library.return_book_with_fine(member_id, book_id)

        if result['success']:
            message = result['message']
            if result['fine'] > 0:
                message += f"\nغرامة: {result['fine']} ريال"
            messagebox.showinfo("نجح", message)
            self.refresh_books_list()
            self.refresh_borrowed_list()
            self.refresh_combos()
        else:
            messagebox.showerror("خطأ", result['message'])

    def refresh_books_list(self):
        """تحديث قائمة الكتب"""
        # مسح القائمة الحالية
        for item in self.books_tree.get_children():
            self.books_tree.delete(item)

        # إضافة الكتب
        for book in self.library.books.values():
            category = getattr(book, 'category', 'غير محدد')
            total_copies = getattr(book, 'total_copies', 1)
            available_copies = getattr(book, 'available_copies', 1)

            if book.is_borrowed:
                status = f"مستعار من قبل {book.borrowed_by}"
            else:
                status = "متاح"

            copies_info = f"{available_copies}/{total_copies}"

            self.books_tree.insert('', 'end', values=(
                book.book_id, book.title, book.author, category, status, copies_info
            ))

    def refresh_members_list(self):
        """تحديث قائمة الأعضاء"""
        for item in self.members_tree.get_children():
            self.members_tree.delete(item)

        for member in self.library.members.values():
            borrowed_count = len(member.borrowed_books)
            self.members_tree.insert('', 'end', values=(
                member.member_id, member.name, member.email,
                borrowed_count, member.join_date
            ))

    def refresh_borrowed_list(self):
        """تحديث قائمة الكتب المستعارة"""
        for item in self.borrowed_tree.get_children():
            self.borrowed_tree.delete(item)

        today = datetime.date.today()

        for book in self.library.books.values():
            if book.is_borrowed:
                days_left = "منتهي الصلاحية"
                if book.due_date:
                    days_diff = (book.due_date - today).days
                    if days_diff >= 0:
                        days_left = f"{days_diff} يوم"
                    else:
                        days_left = f"متأخر {abs(days_diff)} يوم"

                self.borrowed_tree.insert('', 'end', values=(
                    book.borrowed_by, book.title,
                    book.borrowed_date or "غير محدد",
                    book.due_date or "غير محدد",
                    days_left
                ))

    def refresh_combos(self):
        """تحديث قوائم الاختيار"""
        # قائمة الأعضاء
        member_ids = list(self.library.members.keys())
        self.borrow_member_combo['values'] = member_ids
        self.return_member_combo['values'] = member_ids

        # قائمة الكتب المتاحة للاستعارة
        available_books = []
        borrowed_books = []

        for book in self.library.books.values():
            if hasattr(book, 'available_copies') and book.available_copies > 0:
                available_books.append(book.book_id)
            elif not book.is_borrowed:
                available_books.append(book.book_id)

            if book.is_borrowed:
                borrowed_books.append(book.book_id)

        self.borrow_book_combo['values'] = available_books
        self.return_book_combo['values'] = borrowed_books

    def update_statistics(self):
        """تحديث الإحصائيات"""
        report = self.library.generate_report()

        stats_text = f"""
=== إحصائيات المكتبة - Library Statistics ===
تاريخ التقرير: {report['date']}

📚 الكتب:
   • إجمالي الكتب: {report['total_books']}
   • الكتب المتاحة: {report['available_books']}
   • الكتب المستعارة: {report['borrowed_books']}
   • الكتب المتأخرة: {report['overdue_books']}

👥 الأعضاء:
   • إجمالي الأعضاء: {report['total_members']}

💰 المالية:
   • إجمالي الغرامات: {report['total_fines']} ريال

📊 النشاط:
   • إجمالي المعاملات: {report['total_transactions']}

📂 التصنيفات:
   • {', '.join(report['categories']) if report['categories'] else 'لا توجد تصنيفات'}

🔥 أكثر الكتب استعارة:
"""

        if report['popular_books']:
            for i, (book_id, count) in enumerate(report['popular_books'][:5], 1):
                book_title = self.library.books[book_id].title if book_id in self.library.books else book_id
                stats_text += f"   {i}. {book_title}: {count} مرة\n"
        else:
            stats_text += "   • لا توجد بيانات\n"

        stats_text += "\n⭐ أكثر الأعضاء نشاطاً:\n"

        if report['active_members']:
            for i, (member_id, count) in enumerate(report['active_members'][:5], 1):
                member_name = self.library.members[member_id].name if member_id in self.library.members else member_id
                stats_text += f"   {i}. {member_name}: {count} معاملة\n"
        else:
            stats_text += "   • لا توجد بيانات\n"

        # عرض الكتب المتأخرة
        if report['overdue_details']:
            stats_text += "\n⚠️ الكتب المتأخرة:\n"
            for item in report['overdue_details']:
                book = item['book']
                stats_text += f"   • {book.title} - متأخر {item['days_late']} يوم - غرامة: {item['fine']} ريال\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def export_json(self):
        """تصدير البيانات إلى JSON"""
        filename = f"library_data_{datetime.date.today()}.json"
        if self.library.export_to_json(filename):
            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى {filename}")
        else:
            messagebox.showerror("خطأ", "فشل في تصدير البيانات")

    def export_csv(self):
        """تصدير التقرير إلى CSV"""
        filename = f"library_report_{datetime.date.today()}.csv"
        if self.library.export_report_to_csv(filename):
            messagebox.showinfo("نجح", f"تم تصدير التقرير إلى {filename}")
        else:
            messagebox.showerror("خطأ", "فشل في تصدير التقرير")

def main():
    """تشغيل الواجهة الرسومية"""
    root = tk.Tk()
    app = LibraryGUI(root)

    # إعداد إضافي للنافذة
    root.minsize(800, 600)

    # تشغيل التطبيق
    root.mainloop()

if __name__ == "__main__":
    main()