# نظام إدارة المكتبة - محاكاة البرامج
# Library Management System - Program Simulation

## الوصف | Description

هذا المشروع عبارة عن محاكاة لنظام إدارة المكتبة مكتوب بلغة Python. يوفر النظام إمكانيات إدارة الكتب والأعضاء وعمليات الاستعارة والإرجاع.

This project is a Library Management System simulation written in Python. The system provides capabilities for managing books, members, and borrowing/returning operations.

## الملفات | Files

### 1. `library_simulation.py`
الملف الأساسي الذي يحتوي على:
- فئة `Book` لتمثيل الكتب
- فئة `Member` لتمثيل الأعضاء  
- فئة `LibrarySystem` لإدارة النظام
- دالة `main()` لتشغيل المحاكاة التلقائية

### 2. `interactive_library.py`
واجهة تفاعلية تسمح للمستخدم بـ:
- إضافة كتب وأعضاء جدد
- استعارة وإرجاع الكتب
- البحث في الكتب
- عرض الإحصائيات

### 3. `README.md`
ملف التوثيق هذا

## المميزات | Features

✅ **إدارة الكتب**
- إضافة كتب جديدة
- البحث في الكتب
- تتبع حالة الاستعارة

✅ **إدارة الأعضاء**
- تسجيل أعضاء جدد
- تتبع الكتب المستعارة لكل عضو

✅ **عمليات الاستعارة**
- استعارة الكتب مع تحديد تاريخ الإرجاع
- إرجاع الكتب
- تتبع الكتب المتأخرة

✅ **الإحصائيات**
- عدد الكتب والأعضاء
- معدل الاستعارة
- الكتب المتأخرة

✅ **واجهة ثنائية اللغة**
- دعم العربية والإنجليزية

## كيفية التشغيل | How to Run

### 1. تشغيل المحاكاة التلقائية
```bash
python library_simulation.py
```

### 2. تشغيل الواجهة التفاعلية
```bash
python interactive_library.py
```

## أمثلة الاستخدام | Usage Examples

### إضافة كتاب جديد
```python
library = LibrarySystem()
library.add_book("B001", "عنوان الكتاب", "اسم المؤلف", "978-1234567890")
```

### إضافة عضو جديد
```python
library.add_member("M001", "اسم العضو", "<EMAIL>")
```

### استعارة كتاب
```python
library.borrow_book("M001", "B001")
```

### البحث عن كتاب
```python
results = library.search_books("Python")
```

## البيانات التجريبية | Sample Data

عند تشغيل الواجهة التفاعلية، يمكنك اختيار تحميل بيانات تجريبية تتضمن:

**الكتب:**
- الأسود يليق بك - أحلام مستغانمي
- مئة عام من العزلة - غابرييل غارسيا ماركيز  
- البرمجة بلغة Python - محمد أحمد
- تعلم الذكاء الاصطناعي - سارة محمود
- أساسيات علوم الحاسوب - عمر خالد

**الأعضاء:**
- أحمد محمد
- فاطمة علي
- محمد حسن

## المتطلبات | Requirements

- Python 3.6 أو أحدث
- لا توجد مكتبات خارجية مطلوبة (يستخدم المكتبات المدمجة فقط)

## الهيكل التقني | Technical Structure

```
نظام إدارة المكتبة
├── Book (فئة الكتاب)
│   ├── معلومات الكتاب الأساسية
│   ├── حالة الاستعارة
│   └── تواريخ الاستعارة والإرجاع
├── Member (فئة العضو)
│   ├── معلومات العضو الأساسية
│   └── قائمة الكتب المستعارة
└── LibrarySystem (نظام المكتبة)
    ├── إدارة الكتب والأعضاء
    ├── عمليات الاستعارة والإرجاع
    ├── البحث والاستعلام
    └── تسجيل المعاملات
```

## التطوير المستقبلي | Future Development

🔮 **مميزات مقترحة:**
- واجهة رسومية (GUI)
- قاعدة بيانات دائمة
- نظام الغرامات
- تصدير التقارير
- نظام الحجز المسبق
- إشعارات البريد الإلكتروني

## المساهمة | Contributing

نرحب بالمساهمات! يمكنك:
- إضافة مميزات جديدة
- إصلاح الأخطاء
- تحسين التوثيق
- ترجمة النصوص

## الترخيص | License

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتطويري.

---

## الملفات الجديدة | New Files

### 4. `advanced_features.py`
نظام متقدم يحتوي على:
- إدارة النسخ المتعددة للكتب
- نظام الغرامات للكتب المتأخرة
- حجز الكتب
- التصنيفات والفئات
- تصدير البيانات (JSON/CSV)
- تقارير مفصلة

### 5. `gui_library.py`
واجهة رسومية متكاملة مع:
- تبويبات منظمة للكتب والأعضاء
- نظام الاستعارة والإرجاع
- عرض الإحصائيات المباشرة
- تصدير التقارير

### 6. `test_library.py`
مجموعة اختبارات شاملة (16 اختبار)

### 7. `run_gui.py`
ملف تشغيل سريع للواجهة الرسومية

## المميزات المتقدمة الجديدة | New Advanced Features

### 🔥 المميزات المتقدمة:
- ✅ **النسخ المتعددة** - إدارة عدة نسخ من نفس الكتاب
- ✅ **نظام الغرامات** - حساب تلقائي للغرامات على الكتب المتأخرة
- ✅ **حجز الكتب** - إمكانية حجز الكتب المستعارة
- ✅ **التصنيفات** - تنظيم الكتب حسب الفئات
- ✅ **تاريخ العضو** - تتبع جميع معاملات العضو
- ✅ **تصدير البيانات** - JSON و CSV
- ✅ **تقارير متقدمة** - إحصائيات مفصلة وتحليلات
- ✅ **واجهة رسومية** - GUI سهل الاستخدام

### 🖥️ الواجهة الرسومية:
- **تبويب الكتب**: إضافة وعرض الكتب مع التصنيفات
- **تبويب الأعضاء**: إدارة الأعضاء وعضوياتهم
- **تبويب الاستعارة**: عمليات الاستعارة والإرجاع
- **تبويب التقارير**: إحصائيات مباشرة وتصدير البيانات

## طرق التشغيل المختلفة | Different Running Methods

### 1. المحاكاة الأساسية
```bash
python library_simulation.py
```

### 2. الواجهة التفاعلية النصية
```bash
python interactive_library.py
```

### 3. المميزات المتقدمة
```bash
python advanced_features.py
```

### 4. الواجهة الرسومية
```bash
python gui_library.py
# أو
python run_gui.py
```

### 5. تشغيل الاختبارات
```bash
python test_library.py
```

## أمثلة الاستخدام المتقدم | Advanced Usage Examples

### استخدام النظام المتقدم
```python
from advanced_features import AdvancedLibrarySystem

library = AdvancedLibrarySystem()

# إضافة كتاب مع نسخ متعددة
library.add_book_with_category("B001", "Python Programming",
                              "John Doe", "978-123", "تقنية", 5)

# استعارة متقدمة مع تحديد المدة
library.borrow_book_advanced("M001", "B001", days=7)

# إرجاع مع حساب الغرامة
result = library.return_book_with_fine("M001", "B001")
print(f"الغرامة: {result['fine']} ريال")

# حجز كتاب
library.reserve_book("M002", "B001")

# إنشاء تقرير شامل
report = library.generate_report()
print(f"إجمالي الغرامات: {report['total_fines']} ريال")
```

## الملفات المُصدَّرة | Exported Files

عند تشغيل النظام، سيتم إنشاء الملفات التالية:
- `library_data.json` - بيانات المكتبة الكاملة
- `library_report.csv` - تقرير الإحصائيات
- `__pycache__/` - ملفات Python المترجمة

## متطلبات النظام | System Requirements

- **Python 3.6+** (مُختبر على Python 3.13)
- **tkinter** (مدمج مع Python)
- **لا توجد مكتبات خارجية مطلوبة**

## الاختبارات | Testing

تم إنشاء 16 اختبار شامل يغطي:
- ✅ اختبارات الوحدة لكل فئة
- ✅ اختبارات التكامل
- ✅ اختبارات الحالات الاستثنائية
- ✅ اختبارات سير العمل الكامل

```bash
python test_library.py
# النتيجة: ✅ جميع الاختبارات نجحت!
```

## لقطات الشاشة | Screenshots

### الواجهة الرسومية
- تبويبات منظمة وسهلة الاستخدام
- عرض البيانات في جداول تفاعلية
- إحصائيات مباشرة ومفصلة
- أزرار تصدير البيانات

### التقارير
- إحصائيات شاملة للمكتبة
- أكثر الكتب والأعضاء نشاطاً
- تتبع الكتب المتأخرة والغرامات
- تصدير بصيغ متعددة

**ملاحظة:** هذا المشروع مخصص للأغراض التعليمية ومحاكاة البرامج المتقدمة.

**Note:** This project is intended for educational purposes and advanced program simulation.
