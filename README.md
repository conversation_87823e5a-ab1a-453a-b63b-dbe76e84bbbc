# نظام إدارة المكتبة - محاكاة البرامج
# Library Management System - Program Simulation

## الوصف | Description

هذا المشروع عبارة عن محاكاة لنظام إدارة المكتبة مكتوب بلغة Python. يوفر النظام إمكانيات إدارة الكتب والأعضاء وعمليات الاستعارة والإرجاع.

This project is a Library Management System simulation written in Python. The system provides capabilities for managing books, members, and borrowing/returning operations.

## الملفات | Files

### 1. `library_simulation.py`
الملف الأساسي الذي يحتوي على:
- فئة `Book` لتمثيل الكتب
- فئة `Member` لتمثيل الأعضاء  
- فئة `LibrarySystem` لإدارة النظام
- دالة `main()` لتشغيل المحاكاة التلقائية

### 2. `interactive_library.py`
واجهة تفاعلية تسمح للمستخدم بـ:
- إضافة كتب وأعضاء جدد
- استعارة وإرجاع الكتب
- البحث في الكتب
- عرض الإحصائيات

### 3. `README.md`
ملف التوثيق هذا

## المميزات | Features

✅ **إدارة الكتب**
- إضافة كتب جديدة
- البحث في الكتب
- تتبع حالة الاستعارة

✅ **إدارة الأعضاء**
- تسجيل أعضاء جدد
- تتبع الكتب المستعارة لكل عضو

✅ **عمليات الاستعارة**
- استعارة الكتب مع تحديد تاريخ الإرجاع
- إرجاع الكتب
- تتبع الكتب المتأخرة

✅ **الإحصائيات**
- عدد الكتب والأعضاء
- معدل الاستعارة
- الكتب المتأخرة

✅ **واجهة ثنائية اللغة**
- دعم العربية والإنجليزية

## كيفية التشغيل | How to Run

### 1. تشغيل المحاكاة التلقائية
```bash
python library_simulation.py
```

### 2. تشغيل الواجهة التفاعلية
```bash
python interactive_library.py
```

## أمثلة الاستخدام | Usage Examples

### إضافة كتاب جديد
```python
library = LibrarySystem()
library.add_book("B001", "عنوان الكتاب", "اسم المؤلف", "978-1234567890")
```

### إضافة عضو جديد
```python
library.add_member("M001", "اسم العضو", "<EMAIL>")
```

### استعارة كتاب
```python
library.borrow_book("M001", "B001")
```

### البحث عن كتاب
```python
results = library.search_books("Python")
```

## البيانات التجريبية | Sample Data

عند تشغيل الواجهة التفاعلية، يمكنك اختيار تحميل بيانات تجريبية تتضمن:

**الكتب:**
- الأسود يليق بك - أحلام مستغانمي
- مئة عام من العزلة - غابرييل غارسيا ماركيز  
- البرمجة بلغة Python - محمد أحمد
- تعلم الذكاء الاصطناعي - سارة محمود
- أساسيات علوم الحاسوب - عمر خالد

**الأعضاء:**
- أحمد محمد
- فاطمة علي
- محمد حسن

## المتطلبات | Requirements

- Python 3.6 أو أحدث
- لا توجد مكتبات خارجية مطلوبة (يستخدم المكتبات المدمجة فقط)

## الهيكل التقني | Technical Structure

```
نظام إدارة المكتبة
├── Book (فئة الكتاب)
│   ├── معلومات الكتاب الأساسية
│   ├── حالة الاستعارة
│   └── تواريخ الاستعارة والإرجاع
├── Member (فئة العضو)
│   ├── معلومات العضو الأساسية
│   └── قائمة الكتب المستعارة
└── LibrarySystem (نظام المكتبة)
    ├── إدارة الكتب والأعضاء
    ├── عمليات الاستعارة والإرجاع
    ├── البحث والاستعلام
    └── تسجيل المعاملات
```

## التطوير المستقبلي | Future Development

🔮 **مميزات مقترحة:**
- واجهة رسومية (GUI)
- قاعدة بيانات دائمة
- نظام الغرامات
- تصدير التقارير
- نظام الحجز المسبق
- إشعارات البريد الإلكتروني

## المساهمة | Contributing

نرحب بالمساهمات! يمكنك:
- إضافة مميزات جديدة
- إصلاح الأخطاء
- تحسين التوثيق
- ترجمة النصوص

## الترخيص | License

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والتطويري.

---

**ملاحظة:** هذا المشروع مخصص للأغراض التعليمية ومحاكاة البرامج.

**Note:** This project is intended for educational purposes and program simulation.
