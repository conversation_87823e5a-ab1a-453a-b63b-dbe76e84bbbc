#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مميزات متقدمة لنظام إدارة المكتبة
Advanced Features for Library Management System
"""

import json
import csv
import datetime
from typing import List, Dict, Optional
from library_simulation import LibrarySystem, Book, Member

class AdvancedLibrarySystem(LibrarySystem):
    """نظام إدارة المكتبة المتقدم - Advanced Library Management System"""
    
    def __init__(self):
        super().__init__()
        self.fine_rate = 1.0  # غرامة يومية بالريال
        self.max_borrow_days = 14  # أقصى مدة استعارة
        self.max_books_per_member = 5  # أقصى عدد كتب لكل عضو
        self.categories = {}  # تصنيفات الكتب
        self.reservations = {}  # حجوزات الكتب
    
    def add_book_with_category(self, book_id: str, title: str, author: str, 
                              isbn: str, category: str, copies: int = 1) -> bool:
        """إضافة كتاب مع التصنيف وعدد النسخ"""
        if book_id in self.books:
            print(f"خطأ: الكتاب برقم {book_id} موجود بالفعل")
            return False
        
        # إضافة الكتاب الأساسي
        self.books[book_id] = Book(book_id, title, author, isbn)
        self.books[book_id].category = category
        self.books[book_id].total_copies = copies
        self.books[book_id].available_copies = copies
        
        # إضافة التصنيف
        if category not in self.categories:
            self.categories[category] = []
        self.categories[category].append(book_id)
        
        print(f"تم إضافة الكتاب: {title} | التصنيف: {category} | النسخ: {copies}")
        return True
    
    def borrow_book_advanced(self, member_id: str, book_id: str, days: int = None) -> bool:
        """استعارة كتاب متقدمة مع تحديد المدة"""
        if member_id not in self.members:
            print(f"خطأ: العضو برقم {member_id} غير موجود")
            return False
        
        if book_id not in self.books:
            print(f"خطأ: الكتاب برقم {book_id} غير موجود")
            return False
        
        book = self.books[book_id]
        member = self.members[member_id]
        
        # التحقق من عدد الكتب المستعارة
        if len(member.borrowed_books) >= self.max_books_per_member:
            print(f"خطأ: العضو وصل للحد الأقصى من الكتب المستعارة ({self.max_books_per_member})")
            return False
        
        # التحقق من توفر نسخ
        if hasattr(book, 'available_copies') and book.available_copies <= 0:
            print(f"خطأ: لا توجد نسخ متاحة من الكتاب '{book.title}'")
            return False
        
        # تحديد مدة الاستعارة
        borrow_days = days if days else self.max_borrow_days
        if borrow_days > self.max_borrow_days:
            print(f"خطأ: أقصى مدة استعارة هي {self.max_borrow_days} يوم")
            return False
        
        # تنفيذ الاستعارة
        book.is_borrowed = True
        book.borrowed_by = member.name
        book.borrowed_date = datetime.date.today()
        book.due_date = datetime.date.today() + datetime.timedelta(days=borrow_days)
        
        if hasattr(book, 'available_copies'):
            book.available_copies -= 1
        
        member.borrowed_books.append(book_id)
        
        # تسجيل المعاملة
        transaction = {
            'type': 'borrow',
            'member_id': member_id,
            'book_id': book_id,
            'date': datetime.date.today(),
            'due_date': book.due_date,
            'borrow_days': borrow_days
        }
        self.transactions.append(transaction)
        
        print(f"تم استعارة الكتاب '{book.title}' للعضو {member.name}")
        print(f"مدة الاستعارة: {borrow_days} يوم | تاريخ الإرجاع: {book.due_date}")
        return True
    
    def return_book_with_fine(self, member_id: str, book_id: str) -> Dict:
        """إرجاع كتاب مع حساب الغرامة"""
        if member_id not in self.members:
            return {'success': False, 'message': f"خطأ: العضو برقم {member_id} غير موجود"}
        
        if book_id not in self.books:
            return {'success': False, 'message': f"خطأ: الكتاب برقم {book_id} غير موجود"}
        
        book = self.books[book_id]
        member = self.members[member_id]
        
        if not book.is_borrowed or book_id not in member.borrowed_books:
            return {'success': False, 'message': f"خطأ: الكتاب '{book.title}' غير مستعار من قبل هذا العضو"}
        
        # حساب الغرامة
        today = datetime.date.today()
        fine = 0
        days_late = 0
        
        if book.due_date and today > book.due_date:
            days_late = (today - book.due_date).days
            fine = days_late * self.fine_rate
        
        # تنفيذ الإرجاع
        book.is_borrowed = False
        book.borrowed_by = None
        book.borrowed_date = None
        book.due_date = None
        
        if hasattr(book, 'available_copies'):
            book.available_copies += 1
        
        member.borrowed_books.remove(book_id)
        
        # تسجيل المعاملة
        transaction = {
            'type': 'return',
            'member_id': member_id,
            'book_id': book_id,
            'date': today,
            'days_late': days_late,
            'fine': fine
        }
        self.transactions.append(transaction)
        
        result = {
            'success': True,
            'message': f"تم إرجاع الكتاب '{book.title}' من العضو {member.name}",
            'days_late': days_late,
            'fine': fine
        }
        
        if fine > 0:
            result['message'] += f"\nغرامة التأخير: {fine} ريال ({days_late} يوم × {self.fine_rate} ريال)"
        
        print(result['message'])
        return result
    
    def reserve_book(self, member_id: str, book_id: str) -> bool:
        """حجز كتاب"""
        if member_id not in self.members:
            print(f"خطأ: العضو برقم {member_id} غير موجود")
            return False
        
        if book_id not in self.books:
            print(f"خطأ: الكتاب برقم {book_id} غير موجود")
            return False
        
        book = self.books[book_id]
        member = self.members[member_id]
        
        # التحقق من توفر الكتاب
        if not book.is_borrowed and (not hasattr(book, 'available_copies') or book.available_copies > 0):
            print(f"الكتاب '{book.title}' متاح للاستعارة الآن")
            return False
        
        # التحقق من وجود حجز مسبق
        if book_id in self.reservations:
            if member_id in self.reservations[book_id]:
                print(f"لديك حجز مسبق على هذا الكتاب")
                return False
            self.reservations[book_id].append(member_id)
        else:
            self.reservations[book_id] = [member_id]
        
        print(f"تم حجز الكتاب '{book.title}' للعضو {member.name}")
        print(f"ترتيبك في قائمة الانتظار: {len(self.reservations[book_id])}")
        return True
    
    def get_books_by_category(self, category: str) -> List[Book]:
        """الحصول على الكتب حسب التصنيف"""
        if category not in self.categories:
            return []
        
        return [self.books[book_id] for book_id in self.categories[category]]
    
    def get_member_history(self, member_id: str) -> List[Dict]:
        """الحصول على تاريخ العضو"""
        if member_id not in self.members:
            return []
        
        member_transactions = []
        for transaction in self.transactions:
            if transaction['member_id'] == member_id:
                # إضافة معلومات الكتاب
                book = self.books.get(transaction['book_id'])
                if book:
                    transaction_copy = transaction.copy()
                    transaction_copy['book_title'] = book.title
                    transaction_copy['book_author'] = book.author
                    member_transactions.append(transaction_copy)
        
        return member_transactions
    
    def generate_report(self) -> Dict:
        """إنشاء تقرير شامل"""
        today = datetime.date.today()
        
        # إحصائيات أساسية
        total_books = len(self.books)
        total_members = len(self.members)
        borrowed_books = sum(1 for book in self.books.values() if book.is_borrowed)
        available_books = total_books - borrowed_books
        
        # الكتب المتأخرة
        overdue_books = []
        total_fines = 0
        
        for book in self.books.values():
            if book.is_borrowed and book.due_date and book.due_date < today:
                days_late = (today - book.due_date).days
                fine = days_late * self.fine_rate
                overdue_books.append({
                    'book': book,
                    'days_late': days_late,
                    'fine': fine
                })
                total_fines += fine
        
        # أكثر الكتب استعارة
        borrow_count = {}
        for transaction in self.transactions:
            if transaction['type'] == 'borrow':
                book_id = transaction['book_id']
                borrow_count[book_id] = borrow_count.get(book_id, 0) + 1
        
        popular_books = sorted(borrow_count.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # أكثر الأعضاء نشاطاً
        member_activity = {}
        for transaction in self.transactions:
            member_id = transaction['member_id']
            member_activity[member_id] = member_activity.get(member_id, 0) + 1
        
        active_members = sorted(member_activity.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'date': today,
            'total_books': total_books,
            'total_members': total_members,
            'borrowed_books': borrowed_books,
            'available_books': available_books,
            'overdue_books': len(overdue_books),
            'total_fines': total_fines,
            'total_transactions': len(self.transactions),
            'categories': list(self.categories.keys()),
            'popular_books': popular_books,
            'active_members': active_members,
            'overdue_details': overdue_books
        }
    
    def export_to_json(self, filename: str) -> bool:
        """تصدير البيانات إلى JSON"""
        try:
            data = {
                'books': {},
                'members': {},
                'transactions': [],
                'categories': self.categories,
                'reservations': self.reservations
            }
            
            # تصدير الكتب
            for book_id, book in self.books.items():
                data['books'][book_id] = {
                    'book_id': book.book_id,
                    'title': book.title,
                    'author': book.author,
                    'isbn': book.isbn,
                    'is_borrowed': book.is_borrowed,
                    'borrowed_by': book.borrowed_by,
                    'borrowed_date': book.borrowed_date.isoformat() if book.borrowed_date else None,
                    'due_date': book.due_date.isoformat() if book.due_date else None,
                    'category': getattr(book, 'category', 'غير محدد'),
                    'total_copies': getattr(book, 'total_copies', 1),
                    'available_copies': getattr(book, 'available_copies', 1)
                }
            
            # تصدير الأعضاء
            for member_id, member in self.members.items():
                data['members'][member_id] = {
                    'member_id': member.member_id,
                    'name': member.name,
                    'email': member.email,
                    'borrowed_books': member.borrowed_books,
                    'join_date': member.join_date.isoformat()
                }
            
            # تصدير المعاملات
            for transaction in self.transactions:
                trans_copy = transaction.copy()
                if 'date' in trans_copy:
                    trans_copy['date'] = trans_copy['date'].isoformat()
                if 'due_date' in trans_copy:
                    trans_copy['due_date'] = trans_copy['due_date'].isoformat()
                data['transactions'].append(trans_copy)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"تم تصدير البيانات إلى {filename}")
            return True
            
        except Exception as e:
            print(f"خطأ في التصدير: {e}")
            return False
    
    def export_report_to_csv(self, filename: str) -> bool:
        """تصدير تقرير إلى CSV"""
        try:
            report = self.generate_report()
            
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # كتابة الرأس
                writer.writerow(['التقرير', 'القيمة'])
                writer.writerow(['تاريخ التقرير', report['date']])
                writer.writerow(['إجمالي الكتب', report['total_books']])
                writer.writerow(['إجمالي الأعضاء', report['total_members']])
                writer.writerow(['الكتب المستعارة', report['borrowed_books']])
                writer.writerow(['الكتب المتاحة', report['available_books']])
                writer.writerow(['الكتب المتأخرة', report['overdue_books']])
                writer.writerow(['إجمالي الغرامات', f"{report['total_fines']} ريال"])
                writer.writerow(['إجمالي المعاملات', report['total_transactions']])
                
                writer.writerow([])  # سطر فارغ
                writer.writerow(['أكثر الكتب استعارة'])
                writer.writerow(['رقم الكتاب', 'عدد الاستعارات'])
                
                for book_id, count in report['popular_books']:
                    book_title = self.books[book_id].title if book_id in self.books else book_id
                    writer.writerow([book_title, count])
            
            print(f"تم تصدير التقرير إلى {filename}")
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير التقرير: {e}")
            return False

def demo_advanced_features():
    """عرض توضيحي للمميزات المتقدمة"""
    print("=== عرض المميزات المتقدمة لنظام إدارة المكتبة ===")
    print("Advanced Library Management System Features Demo")
    print("="*60)
    
    # إنشاء النظام المتقدم
    library = AdvancedLibrarySystem()
    
    # إضافة كتب مع تصنيفات
    print("\n--- إضافة كتب مع التصنيفات ---")
    library.add_book_with_category("B001", "البرمجة بـ Python", "أحمد محمد", "978-111", "تقنية", 3)
    library.add_book_with_category("B002", "الذكاء الاصطناعي", "سارة أحمد", "978-222", "تقنية", 2)
    library.add_book_with_category("B003", "الأسود يليق بك", "أحلام مستغانمي", "978-333", "أدب", 1)
    library.add_book_with_category("B004", "تاريخ الحضارات", "محمد علي", "978-444", "تاريخ", 2)
    
    # إضافة أعضاء
    print("\n--- إضافة أعضاء ---")
    library.add_member("M001", "أحمد محمد", "<EMAIL>")
    library.add_member("M002", "فاطمة علي", "<EMAIL>")
    library.add_member("M003", "محمد حسن", "<EMAIL>")
    
    # استعارة متقدمة
    print("\n--- الاستعارة المتقدمة ---")
    library.borrow_book_advanced("M001", "B001", 10)  # 10 أيام
    library.borrow_book_advanced("M002", "B002", 7)   # 7 أيام
    library.borrow_book_advanced("M001", "B003")      # المدة الافتراضية
    
    # محاولة حجز كتاب مستعار
    print("\n--- حجز الكتب ---")
    library.reserve_book("M003", "B003")
    
    # عرض الكتب حسب التصنيف
    print("\n--- الكتب حسب التصنيف ---")
    tech_books = library.get_books_by_category("تقنية")
    print(f"كتب التقنية ({len(tech_books)}):")
    for book in tech_books:
        status = f"متاح ({book.available_copies})" if hasattr(book, 'available_copies') else "متاح"
        if book.is_borrowed:
            status = f"مستعار من قبل {book.borrowed_by}"
        print(f"  - {book.title} | {status}")
    
    # إرجاع مع غرامة (محاكاة تأخير)
    print("\n--- إرجاع مع حساب الغرامة ---")
    # تعديل تاريخ الاستحقاق لمحاكاة التأخير
    library.books["B001"].due_date = datetime.date.today() - datetime.timedelta(days=3)
    result = library.return_book_with_fine("M001", "B001")
    
    # تاريخ العضو
    print("\n--- تاريخ العضو ---")
    history = library.get_member_history("M001")
    print(f"تاريخ العضو أحمد محمد ({len(history)} معاملة):")
    for trans in history:
        print(f"  - {trans['type']}: {trans['book_title']} في {trans['date']}")
    
    # إنشاء تقرير شامل
    print("\n--- التقرير الشامل ---")
    report = library.generate_report()
    print(f"إجمالي الكتب: {report['total_books']}")
    print(f"إجمالي الأعضاء: {report['total_members']}")
    print(f"الكتب المستعارة: {report['borrowed_books']}")
    print(f"الكتب المتأخرة: {report['overdue_books']}")
    print(f"إجمالي الغرامات: {report['total_fines']} ريال")
    print(f"التصنيفات: {', '.join(report['categories'])}")
    
    if report['popular_books']:
        print("\nأكثر الكتب استعارة:")
        for book_id, count in report['popular_books'][:3]:
            book_title = library.books[book_id].title
            print(f"  - {book_title}: {count} مرة")
    
    # تصدير البيانات
    print("\n--- تصدير البيانات ---")
    library.export_to_json("library_data.json")
    library.export_report_to_csv("library_report.csv")
    
    print("\n--- انتهاء العرض التوضيحي ---")

if __name__ == "__main__":
    demo_advanced_features()
