#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة تفاعلية لنظام إدارة المكتبة
Interactive Library Management System
"""

from library_simulation import LibrarySystem

def display_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*50)
    print("         نظام إدارة المكتبة التفاعلي")
    print("    Interactive Library Management System")
    print("="*50)
    print("1. إضافة كتاب جديد (Add Book)")
    print("2. إضافة عضو جديد (Add Member)")
    print("3. استعارة كتاب (Borrow Book)")
    print("4. إرجاع كتاب (Return Book)")
    print("5. البحث عن كتاب (Search Books)")
    print("6. عرض جميع الكتب (Display All Books)")
    print("7. عرض جميع الأعضاء (Display All Members)")
    print("8. عرض الكتب المتأخرة (Display Overdue Books)")
    print("9. إحصائيات المكتبة (Library Statistics)")
    print("0. خروج (Exit)")
    print("="*50)

def get_user_input(prompt: str) -> str:
    """الحصول على مدخلات المستخدم"""
    return input(f"{prompt}: ").strip()

def add_book_interactive(library: LibrarySystem):
    """إضافة كتاب بشكل تفاعلي"""
    print("\n--- إضافة كتاب جديد ---")
    book_id = get_user_input("رقم الكتاب (Book ID)")
    title = get_user_input("عنوان الكتاب (Title)")
    author = get_user_input("المؤلف (Author)")
    isbn = get_user_input("رقم ISBN")
    
    if book_id and title and author and isbn:
        library.add_book(book_id, title, author, isbn)
    else:
        print("خطأ: جميع الحقول مطلوبة")

def add_member_interactive(library: LibrarySystem):
    """إضافة عضو بشكل تفاعلي"""
    print("\n--- إضافة عضو جديد ---")
    member_id = get_user_input("رقم العضو (Member ID)")
    name = get_user_input("الاسم (Name)")
    email = get_user_input("البريد الإلكتروني (Email)")
    
    if member_id and name and email:
        library.add_member(member_id, name, email)
    else:
        print("خطأ: جميع الحقول مطلوبة")

def borrow_book_interactive(library: LibrarySystem):
    """استعارة كتاب بشكل تفاعلي"""
    print("\n--- استعارة كتاب ---")
    member_id = get_user_input("رقم العضو (Member ID)")
    book_id = get_user_input("رقم الكتاب (Book ID)")
    
    if member_id and book_id:
        library.borrow_book(member_id, book_id)
    else:
        print("خطأ: جميع الحقول مطلوبة")

def return_book_interactive(library: LibrarySystem):
    """إرجاع كتاب بشكل تفاعلي"""
    print("\n--- إرجاع كتاب ---")
    member_id = get_user_input("رقم العضو (Member ID)")
    book_id = get_user_input("رقم الكتاب (Book ID)")
    
    if member_id and book_id:
        library.return_book(member_id, book_id)
    else:
        print("خطأ: جميع الحقول مطلوبة")

def search_books_interactive(library: LibrarySystem):
    """البحث عن كتب بشكل تفاعلي"""
    print("\n--- البحث عن كتاب ---")
    query = get_user_input("كلمة البحث (Search Query)")
    
    if query:
        results = library.search_books(query)
        if results:
            print(f"\nتم العثور على {len(results)} نتيجة:")
            for i, book in enumerate(results, 1):
                print(f"{i}. {book}")
        else:
            print("لم يتم العثور على نتائج")
    else:
        print("خطأ: يرجى إدخال كلمة البحث")

def display_statistics(library: LibrarySystem):
    """عرض إحصائيات المكتبة"""
    print("\n--- إحصائيات المكتبة ---")
    
    total_books = len(library.books)
    total_members = len(library.members)
    borrowed_books = sum(1 for book in library.books.values() if book.is_borrowed)
    available_books = total_books - borrowed_books
    total_transactions = len(library.transactions)
    
    print(f"إجمالي الكتب: {total_books}")
    print(f"الكتب المتاحة: {available_books}")
    print(f"الكتب المستعارة: {borrowed_books}")
    print(f"إجمالي الأعضاء: {total_members}")
    print(f"إجمالي المعاملات: {total_transactions}")
    
    if total_books > 0:
        borrowing_rate = (borrowed_books / total_books) * 100
        print(f"معدل الاستعارة: {borrowing_rate:.1f}%")

def initialize_sample_data(library: LibrarySystem):
    """تهيئة بيانات تجريبية"""
    print("تهيئة بيانات تجريبية...")
    
    # إضافة كتب تجريبية
    sample_books = [
        ("B001", "الأسود يليق بك", "أحلام مستغانمي", "978-123456789"),
        ("B002", "مئة عام من العزلة", "غابرييل غارسيا ماركيز", "978-987654321"),
        ("B003", "البرمجة بلغة Python", "محمد أحمد", "978-111222333"),
        ("B004", "تعلم الذكاء الاصطناعي", "سارة محمود", "978-444555666"),
        ("B005", "أساسيات علوم الحاسوب", "عمر خالد", "978-777888999")
    ]
    
    for book_data in sample_books:
        library.add_book(*book_data)
    
    # إضافة أعضاء تجريبيين
    sample_members = [
        ("M001", "أحمد محمد", "<EMAIL>"),
        ("M002", "فاطمة علي", "<EMAIL>"),
        ("M003", "محمد حسن", "<EMAIL>")
    ]
    
    for member_data in sample_members:
        library.add_member(*member_data)
    
    print("تم تحميل البيانات التجريبية بنجاح!")

def main():
    """الدالة الرئيسية للواجهة التفاعلية"""
    library = LibrarySystem()
    
    print("مرحباً بك في نظام إدارة المكتبة التفاعلي!")
    
    # سؤال المستخدم عن تحميل البيانات التجريبية
    load_sample = input("هل تريد تحميل بيانات تجريبية؟ (y/n): ").lower().strip()
    if load_sample in ['y', 'yes', 'نعم', 'ن']:
        initialize_sample_data(library)
    
    while True:
        display_menu()
        choice = get_user_input("اختر رقم العملية")
        
        try:
            if choice == '1':
                add_book_interactive(library)
            elif choice == '2':
                add_member_interactive(library)
            elif choice == '3':
                borrow_book_interactive(library)
            elif choice == '4':
                return_book_interactive(library)
            elif choice == '5':
                search_books_interactive(library)
            elif choice == '6':
                library.display_all_books()
            elif choice == '7':
                library.display_all_members()
            elif choice == '8':
                library.display_overdue_books()
            elif choice == '9':
                display_statistics(library)
            elif choice == '0':
                print("شكراً لاستخدام نظام إدارة المكتبة!")
                print("Thank you for using the Library Management System!")
                break
            else:
                print("خيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n\nتم إيقاف البرنامج بواسطة المستخدم")
            break
        except Exception as e:
            print(f"حدث خطأ: {e}")
        
        # انتظار المستخدم قبل العودة للقائمة
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
