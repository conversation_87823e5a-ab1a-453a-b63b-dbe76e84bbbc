#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة نظام إدارة المكتبة
Library Management System Simulation
"""

import datetime
from typing import List, Dict, Optional

class Book:
    """فئة الكتاب - Book class"""
    
    def __init__(self, book_id: str, title: str, author: str, isbn: str):
        self.book_id = book_id
        self.title = title
        self.author = author
        self.isbn = isbn
        self.is_borrowed = False
        self.borrowed_by = None
        self.borrowed_date = None
        self.due_date = None
    
    def __str__(self):
        status = "متاح" if not self.is_borrowed else f"مستعار من قبل {self.borrowed_by}"
        return f"الكتاب: {self.title} | المؤلف: {self.author} | الحالة: {status}"

class Member:
    """فئة العضو - Member class"""
    
    def __init__(self, member_id: str, name: str, email: str):
        self.member_id = member_id
        self.name = name
        self.email = email
        self.borrowed_books: List[str] = []
        self.join_date = datetime.date.today()
    
    def __str__(self):
        return f"العضو: {self.name} | الرقم: {self.member_id} | الكتب المستعارة: {len(self.borrowed_books)}"

class LibrarySystem:
    """نظام إدارة المكتبة - Library Management System"""
    
    def __init__(self):
        self.books: Dict[str, Book] = {}
        self.members: Dict[str, Member] = {}
        self.transactions: List[Dict] = []
    
    def add_book(self, book_id: str, title: str, author: str, isbn: str) -> bool:
        """إضافة كتاب جديد - Add new book"""
        if book_id in self.books:
            print(f"خطأ: الكتاب برقم {book_id} موجود بالفعل")
            return False
        
        self.books[book_id] = Book(book_id, title, author, isbn)
        print(f"تم إضافة الكتاب: {title}")
        return True
    
    def add_member(self, member_id: str, name: str, email: str) -> bool:
        """إضافة عضو جديد - Add new member"""
        if member_id in self.members:
            print(f"خطأ: العضو برقم {member_id} موجود بالفعل")
            return False
        
        self.members[member_id] = Member(member_id, name, email)
        print(f"تم إضافة العضو: {name}")
        return True
    
    def borrow_book(self, member_id: str, book_id: str) -> bool:
        """استعارة كتاب - Borrow book"""
        if member_id not in self.members:
            print(f"خطأ: العضو برقم {member_id} غير موجود")
            return False
        
        if book_id not in self.books:
            print(f"خطأ: الكتاب برقم {book_id} غير موجود")
            return False
        
        book = self.books[book_id]
        member = self.members[member_id]
        
        if book.is_borrowed:
            print(f"خطأ: الكتاب '{book.title}' مستعار بالفعل")
            return False
        
        # تنفيذ الاستعارة
        book.is_borrowed = True
        book.borrowed_by = member.name
        book.borrowed_date = datetime.date.today()
        book.due_date = datetime.date.today() + datetime.timedelta(days=14)
        
        member.borrowed_books.append(book_id)
        
        # تسجيل المعاملة
        transaction = {
            'type': 'borrow',
            'member_id': member_id,
            'book_id': book_id,
            'date': datetime.date.today(),
            'due_date': book.due_date
        }
        self.transactions.append(transaction)
        
        print(f"تم استعارة الكتاب '{book.title}' للعضو {member.name}")
        print(f"تاريخ الإرجاع المتوقع: {book.due_date}")
        return True
    
    def return_book(self, member_id: str, book_id: str) -> bool:
        """إرجاع كتاب - Return book"""
        if member_id not in self.members:
            print(f"خطأ: العضو برقم {member_id} غير موجود")
            return False
        
        if book_id not in self.books:
            print(f"خطأ: الكتاب برقم {book_id} غير موجود")
            return False
        
        book = self.books[book_id]
        member = self.members[member_id]
        
        if not book.is_borrowed or book_id not in member.borrowed_books:
            print(f"خطأ: الكتاب '{book.title}' غير مستعار من قبل هذا العضو")
            return False
        
        # تنفيذ الإرجاع
        book.is_borrowed = False
        book.borrowed_by = None
        book.borrowed_date = None
        book.due_date = None
        
        member.borrowed_books.remove(book_id)
        
        # تسجيل المعاملة
        transaction = {
            'type': 'return',
            'member_id': member_id,
            'book_id': book_id,
            'date': datetime.date.today()
        }
        self.transactions.append(transaction)
        
        print(f"تم إرجاع الكتاب '{book.title}' من العضو {member.name}")
        return True
    
    def search_books(self, query: str) -> List[Book]:
        """البحث عن الكتب - Search books"""
        results = []
        query_lower = query.lower()
        
        for book in self.books.values():
            if (query_lower in book.title.lower() or 
                query_lower in book.author.lower() or 
                query_lower in book.isbn.lower()):
                results.append(book)
        
        return results
    
    def display_all_books(self):
        """عرض جميع الكتب - Display all books"""
        if not self.books:
            print("لا توجد كتب في المكتبة")
            return
        
        print("\n=== جميع الكتب في المكتبة ===")
        for book in self.books.values():
            print(book)
    
    def display_all_members(self):
        """عرض جميع الأعضاء - Display all members"""
        if not self.members:
            print("لا يوجد أعضاء مسجلون")
            return
        
        print("\n=== جميع الأعضاء المسجلون ===")
        for member in self.members.values():
            print(member)
    
    def display_overdue_books(self):
        """عرض الكتب المتأخرة - Display overdue books"""
        today = datetime.date.today()
        overdue_books = []
        
        for book in self.books.values():
            if book.is_borrowed and book.due_date and book.due_date < today:
                overdue_books.append(book)
        
        if not overdue_books:
            print("لا توجد كتب متأخرة")
            return
        
        print("\n=== الكتب المتأخرة ===")
        for book in overdue_books:
            days_overdue = (today - book.due_date).days
            print(f"{book} | متأخر بـ {days_overdue} يوم")

def main():
    """الدالة الرئيسية - Main function"""
    library = LibrarySystem()
    
    print("=== مرحباً بك في نظام إدارة المكتبة ===")
    print("Library Management System Simulation")
    
    # إضافة بيانات تجريبية
    print("\n--- إضافة بيانات تجريبية ---")
    library.add_book("B001", "الأسود يليق بك", "أحلام مستغانمي", "978-123456789")
    library.add_book("B002", "مئة عام من العزلة", "غابرييل غارسيا ماركيز", "978-987654321")
    library.add_book("B003", "البرمجة بلغة Python", "محمد أحمد", "978-111222333")
    
    library.add_member("M001", "أحمد محمد", "<EMAIL>")
    library.add_member("M002", "فاطمة علي", "<EMAIL>")
    
    # عرض جميع الكتب والأعضاء
    library.display_all_books()
    library.display_all_members()
    
    # تجربة الاستعارة والإرجاع
    print("\n--- تجربة الاستعارة ---")
    library.borrow_book("M001", "B001")
    library.borrow_book("M002", "B002")
    
    print("\n--- حالة الكتب بعد الاستعارة ---")
    library.display_all_books()
    
    print("\n--- تجربة الإرجاع ---")
    library.return_book("M001", "B001")
    
    print("\n--- حالة الكتب بعد الإرجاع ---")
    library.display_all_books()
    
    # تجربة البحث
    print("\n--- تجربة البحث ---")
    search_results = library.search_books("Python")
    if search_results:
        print("نتائج البحث:")
        for book in search_results:
            print(f"  - {book}")
    else:
        print("لم يتم العثور على نتائج")
    
    print("\n--- انتهاء المحاكاة ---")

if __name__ == "__main__":
    main()
